/// <reference types="@testing-library/cypress" />
import { DateTime, Settings } from 'luxon'

import {
  PROMPT_KNOWN_DRIVER_TYPE_FIELD_NAME,
  type GetReportOptionsV2Api,
} from 'api/reports/types'
import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { geofenceGroupsMock } from 'src/cypress-ct/mocks/endpoints/geofence'
import {
  dateTimeRangeEndExceedToday,
  timezoneToParseDates,
  vehicleGroupsMock,
  vehiclesMock,
} from 'src/cypress-ct/mocks/endpoints/report'
import { VEHICLE_ALL_VALUE } from 'src/modules/components/unconnected/MultipleSelect/shared'
import { messages } from 'src/shared/forms/messages'

import {
  DataToReceiveCustomPeriodOptions,
  DataToReceiveDurationOptions,
  getNextDayOfWeekOrMonth,
  ReportFrequencyOptions,
} from './util'

Settings.defaultZone = timezoneToParseDates

const { mockState } = duxsMocks.user({})

const newInvalidEmail = 't.p@cartrack'
export const newValidEmail = '<EMAIL>'
// eslint-disable-next-line sonarjs/no-hardcoded-credentials
export const passwordText = 'passwor1'
export const numberValue = '15'

const today = DateTime.local().set({ hour: 12, minute: 10 })

const dateFormat = 'yyyy/LL/dd'
const dateTimeFormat = 'yyyyLLddHHmm'

const type5Left = '{leftArrow}{leftArrow}{leftArrow}{leftArrow}{leftArrow}'
const type3Left = '{leftArrow}{leftArrow}{leftArrow}'

const invalidDateRange = {
  // End date is before Start date
  start: today.minus({ day: 10 }).toFormat(dateFormat),
  end: today.minus({ day: 20 }).toFormat(dateFormat),
}

export const validDateRange = {
  start: today.minus({ month: 1 }),
  end: today.minus({ day: 20 }),
}

const validDateRangeStringValue = {
  start: validDateRange.start.toFormat(dateFormat),
  end: validDateRange.end.toFormat(dateFormat),
}

const invalidDateTimeRangeInput = {
  // End date time is before Start date time
  start: today.plus({ day: 1, hour: 10 }).toFormat(dateTimeFormat), // 2023-11-15 05:00
  end: today.minus({ day: 2 }).toFormat(dateTimeFormat), // 2023-11-15 15:00
}

export const validDateTimeRangeInput = {
  start: today.minus({ month: 1, hour: 10 }), // 2023-11-15 05:00
  end: today.minus({ month: 1 }), // 2023-11-15 15:00
}

export const validDateTimeRangeInputString = {
  start: validDateTimeRangeInput.start.toFormat(dateTimeFormat), // 2023-11-15 05:00
  end: validDateTimeRangeInput.end.toFormat(dateTimeFormat), // 2023-11-15 15:00
}

export const dummyText = 'dummy text'

type Report = GetReportOptionsV2Api.ApiOutput[number]

export const testSelectReport = (report: Report) => {
  cy.findByTestId(`Reports-AllReports-SidebarItem-${report.report_id}-ExportButton`).as(
    'exportButton',
  )

  cy.findByTestId('Report-ExportDrawer').should('not.exist')

  cy.get('@exportButton').should('not.be.visible').click({ force: true })

  cy.findByTestId('Report-ExportDrawer').should('be.visible')

  cy.findByTestId('Report-ExportReportName').should('have.text', report.name)

  if (report.report_description) {
    cy.findByTestId('Report-ExportReportDescription').should(
      'have.text',
      report.report_description,
    )
  }
}

export const checkRegistrationPromptValue = (value: string) => {
  // All vehicles is selected by default
  cy.findByTestId('ReportForm-Registration-Single')
    .find('input')
    .should('have.value', value)
}

export const fillRegistrationPromptSingleValue = ({
  value,
  includeAll,
}: {
  value: string
  includeAll: boolean
}) => {
  cy.findByTestId('ReportForm-Registration-Single').find('input').click()

  cy.findByRole('option', { name: new RegExp(VEHICLE_ALL_VALUE.label, 'i') }).should(
    `${includeAll ? '' : 'not.'}exist`,
  )

  cy.findByRole('option', { name: new RegExp(value, 'i') }).click()
}

export const checkRegistrationMultiplePromptWidget = () => {
  cy.findByTestId('ReportForm-Registration-Multiple').should('exist')

  cy.findByTestId('ReportForm-Registration-Multiple')
    .find('input')
    .should('have.value', '')
    .click()

  cy.findByRole('option', { name: VEHICLE_ALL_VALUE.label }).click()

  cy.findByRole('option', { name: VEHICLE_ALL_VALUE.label })
    .find('input')
    .should('be.checked')

  // choose vehicle would uncheck all value
  // vehicle id, chec
  cy.findByRole('option', { name: new RegExp(vehiclesMock[0].name, 'i') }).click()

  // it should be checked
  cy.findByRole('option', { name: new RegExp(vehiclesMock[0].name, 'i') })
    .find('input')
    .should('be.checked')

  // value all should be unchecked
  cy.findByRole('option', { name: VEHICLE_ALL_VALUE.label })
    .find('input')
    .should('not.be.checked')

  // check vehicle group
  cy.findByRole('option', { name: new RegExp(vehicleGroupsMock[0].name, 'i') }).click()

  // it should be checked
  cy.findByRole('option', { name: new RegExp(vehicleGroupsMock[0].name, 'i') })
    .find('input')
    .should('be.checked')

  // check all
  cy.findByRole('option', { name: VEHICLE_ALL_VALUE.label }).click()

  // vehicle group should be unchecked
  cy.findByRole('option', { name: new RegExp(vehicleGroupsMock[0].name, 'i') })
    .find('input')
    .should('not.be.checked')

  // vehicle should be unchecked
  cy.findByRole('option', { name: new RegExp(vehiclesMock[0].name, 'i') })
    .find('input')
    .should('not.be.checked')

  // uncheck all
  cy.findByRole('option', { name: VEHICLE_ALL_VALUE.label }).click()

  // Click outside to close list
  cy.findByTestId('Report-ExportReportName').click()
}

export const fillRegistrationMultipleWidget = ({
  vehicle,
  group,
}: {
  vehicle: string
  group: string
}) => {
  cy.findByTestId('ReportForm-Registration-Multiple').find('input').click()

  // vehicle id
  cy.findByRole('option', {
    name: new RegExp(vehicle, 'i'),
  }).click()

  // vehicle group
  cy.findByRole('option', {
    name: new RegExp(group, 'i'),
  }).click()

  // Click outside to close list
  cy.findByTestId('Report-ExportReportName').click()
}

export const markDateRangeStartEndComponent = () => {
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiPickersSectionList-root')
    .first()
    .as('startDateInput')
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiPickersSectionList-root')
    .last()
    .as('endDateInput')
}

const checkDatesAreOnSameDay = (
  firstDateValue: JQuery<HTMLElement>,
  secondDateValue: DateTime,
) => {
  const dateValue = firstDateValue.find('.MuiPickersSectionList-sectionContent')

  const year = dateValue.first()
  const month = dateValue.eq(1)
  const date = dateValue.eq(2)

  expect(year.text()).to.equal(secondDateValue.year.toString())
  expect(month.text()).to.equal(secondDateValue.month.toString().padStart(2, '0'))
  expect(date.text()).to.equal(secondDateValue.day.toString().padStart(2, '0'))
}

export const checkDateRangeShortcuts = () => {
  // open calendar view
  cy.get('@startDateInput').click()

  cy.get('.MuiPickersLayout-root')
    .find('.MuiPickersLayout-shortcuts')
    .children()
    .as('shortcuts')

  // Check select 'Last 7 days' shortcut
  cy.get('@shortcuts')
    .eq(0)
    .contains(/Last 7 days/i)
    .click()
  cy.get('@startDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local().minus({ days: 7 - 1 }))
  })
  cy.get('@endDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local())
  })

  // Check select 'Last 15 days' shortcut
  cy.get('@startDateInput').click()
  cy.get('@shortcuts')
    .eq(1)
    .contains(/Last 15 days/i)
    .click()
  cy.get('@startDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local().minus({ days: 15 - 1 }))
  })
  cy.get('@endDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local())
  })

  // Check select 'This Month' shortcut
  cy.get('@startDateInput').click()
  cy.get('@shortcuts')
    .eq(2)
    .contains(/this month/i)
    .click()
  cy.get('@startDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local().startOf('month'))
  })
  cy.get('@endDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local())
  })

  // Check select 'Last Month' shortcut
  cy.get('@startDateInput').click()
  cy.get('@shortcuts')
    .eq(3)
    .contains(/last month/i)
    .click()
  cy.get('@startDateInput').then(($input) => {
    checkDatesAreOnSameDay(
      $input,
      DateTime.local().minus({ months: 1 }).startOf('month'),
    )
  })
  cy.get('@endDateInput').then(($input) => {
    checkDatesAreOnSameDay($input, DateTime.local().minus({ months: 1 }).endOf('month'))
  })

  // Check select 'Clear' shortcut
  cy.get('@startDateInput').click()
  cy.get('@shortcuts').eq(4).contains(/Clear/i).click()
  cy.get('@startDateInput').should('have.value', '')
  cy.get('@endDateInput').should('have.value', '')
}

export const checkDateRangePrompt = () => {
  cy.findByTestId('ReportForm-DateRange')
    .should('exist')
    .should('contain.text', 'Start')
    .should('contain.text', 'End')
}

export const fillValidDateRange = (dateRange?: [DateTime, DateTime]) => {
  const startTime = dateRange
    ? dateRange[0].toFormat(dateFormat)
    : validDateRangeStringValue.start
  const endTime = dateRange
    ? dateRange[1].toFormat(dateFormat)
    : validDateRangeStringValue.end
  cy.get('@startDateInput').type(`${type3Left}${startTime}`)
  cy.get('@endDateInput').type(`${type3Left}${endTime}`)
}

export const fillInvalidDateRange = () => {
  cy.log('__******__ __fillInvalidDateRange__ __******__')
  // start date after end date
  cy.get('@startDateInput').type(`${type3Left}${invalidDateRange.start}`)
  cy.findByTestId('ReportForm-DateRange').should('contain.text', messages.required)
  cy.get('@endDateInput').type(`${type3Left}${invalidDateRange.end}`)
  cy.findByTestId('ReportForm-DateRange').should(
    'contain.text',
    'Start Date must be lower than End Date',
  )

  // fill the valid to remove the error
  fillValidDateRange()
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiFormHelperText-root')
    .should('not.exist')

  // fill the over date range
  cy.get('@startDateInput').type(`${type3Left}1990/12/12`)
  cy.get('@endDateInput').type(`${type3Left}2991/12/12`)
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Date range must be before the current date')
  cy.get('@startDateInput').type(`${type3Left}${validDateRangeStringValue.start}`)
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Date range must be before the current date')

  // fill the end out of current date
  cy.get('@endDateInput').type(
    `${type3Left}${today
      .plus({ day: dateTimeRangeEndExceedToday - 1 })
      .toFormat(dateFormat)}`,
  )
  cy.findByTestId('ReportForm-DateRange')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Date range must be before the current date')
}

export const markDateTimeRangePrompt = () => {
  cy.findByTestId('ReportForm-DateTimeRange')
    .find('.MuiPickersSectionList-root')
    .first()
    .as('startDateTimeInput')

  cy.findByTestId('ReportForm-DateTimeRange')
    .find('.MuiPickersSectionList-root')
    .last()
    .as('endDateTimeInput')
}

export const checkDateTimeRangePrompt = () => {
  cy.findByTestId('ReportForm-DateTimeRange')
    .should('exist')
    .should('contain.text', 'Start Date & Time')
    .should('contain.text', 'End Date & Time')
}

export const fillInvalidDateTimeRangePrompt = () => {
  cy.log('__******__ __fillInvalidDateTimeRangePrompt__ __******__')
  // start date time after end date time
  cy.get('@startDateTimeInput').type(invalidDateTimeRangeInput.start)
  cy.findByTestId('ReportForm-DateTimeRange').should('contain.text', messages.required)
  cy.get('@endDateTimeInput').type(invalidDateTimeRangeInput.end)
  cy.findByTestId('ReportForm-DateTimeRange').should(
    'contain.text',
    'Start Date must be lower than End Date',
  )

  // fill the over date time range
  cy.get('@startDateTimeInput').type(`${type5Left}199012121212`)
  cy.get('@endDateTimeInput').type(`${type5Left}299112121212`)

  // first find the start datetime
  cy.findByTestId('ReportForm-DateTimeRange')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Start Date must not be exceed min date')

  // fill the valid start datetime
  cy.get('@startDateTimeInput').type(
    `${type5Left}${validDateTimeRangeInputString.start}`,
  )

  // the end date time exceeds
  cy.findByTestId('ReportForm-DateTimeRange')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'End Date must not be exceed max date')

  // // fill the valid start datetime
  // cy.get('@endDateTimeInput').type(`${type5Left}${validDateTimeRangeInputString.end}`)
  // cy.findByTestId('ReportForm-DateTimeRange')
  //   .find('.MuiFormHelperText-root')
  //   .should('not.exist')

  // // fill the end out of current date
  // cy.get('@endDateTimeInput').type(
  //   `${type5Left}${today
  //     .plus({ day: dateTimeRangeEndExceedToday - 1 })
  //     .toFormat(dateTimeFormat)}`,
  // )
  // cy.findByTestId('ReportForm-DateTimeRange')
  //   .find('.MuiFormHelperText-root')
  //   .should('contain.text', 'Date range must be before the current date')
}

export const fillValidDateTimeRangePrompt = (dateRange?: [DateTime, DateTime]) => {
  cy.log('__******__ __fillValidDateTimeRangePrompt__ __******__')
  const startTime = dateRange
    ? dateRange[0].toFormat(dateTimeFormat)
    : validDateTimeRangeInputString.start
  const endTime = dateRange
    ? dateRange[1].toFormat(dateTimeFormat)
    : validDateTimeRangeInputString.end
  // fill valid date time values
  cy.get('@startDateTimeInput').type(`${type5Left}${startTime}`)
  cy.get('@endDateTimeInput').type(`${type5Left}${endTime}`)
  // click the header to hide the date picker dialog
  cy.findByTestId('Report-ExportReportName').click()
  cy.findByTestId('ReportForm-DateTimeRange').should(
    'not.contain.text',
    'Start Date must be lower than End Date',
  )
}

export const checkGeofencePrompt = (report: Report) => {
  cy.findByTestId('ReportForm-Geofence')
    .should('exist')
    .should(
      'contain.text',
      report.prompts?.find((i) => i.identifier === 'geofence_id')?.name,
    )
  cy.findByTestId('ReportForm-Geofence')
    .find('input')
    .should(($input) => {
      const inputValue = $input.val() as string
      expect(inputValue).to.match(/no selection/i)
    })
}

export const fillGeofenceWidget = (value: string) => {
  cy.findByTestId('ReportForm-Geofence').click()
  cy.findByRole('option', {
    name: new RegExp(value, 'i'),
  }).click()
}

export const checkGeofenceGroupPrompt = (report: Report) => {
  cy.findByTestId('ReportForm-GeofenceGroup')
    .should('exist')
    .should(
      'contain.text',
      report.prompts?.find((i) => i.identifier === 'geofence_group')?.name,
    )
}

export const fillGeofenceGroupWidget = () => {
  cy.findByTestId('ReportForm-GeofenceGroup')
    .find('input')
    .should(($input) => {
      const inputValue = $input.val() as string
      expect(inputValue).to.match(/no selection/i)
    })
    .click()
  cy.findByRole('option', {
    name: new RegExp(geofenceGroupsMock['323'].name, 'i'),
  }).click()
}

export const checkTextInputPrompt = ({
  report,
  identifier,
  type,
}: {
  report: Report
  identifier: string
  type: 'one-time' | 'recurring'
}) => {
  cy.findByTestId(
    `ReportForm-${
      type === 'one-time' ? 'OneTime' : 'Recurring'
    }-StringType-${identifier}`,
  )
    .should('exist')
    .should(
      'contain.text',
      report.prompts?.find((i) => i.identifier === identifier)?.name,
    )
    .find('input')
    .as('promptTextInput')

  cy.get('@promptTextInput').type('test')
  cy.get('@promptTextInput').clear()
  cy.findByTestId(
    `ReportForm-${
      type === 'one-time' ? 'OneTime' : 'Recurring'
    }-StringType-${identifier}`,
  ).should('contain.text', messages.required)
}

export const fillTextInputPrompt = ({
  identifier,
  type,
  typeText = dummyText,
}: {
  identifier: string
  type: 'one-time' | 'recurring'
  typeText?: string
}) => {
  cy.findByTestId(
    `ReportForm-${
      type === 'one-time' ? 'OneTime' : 'Recurring'
    }-StringType-${identifier}`,
  ).type(typeText)
}

export const checkNumberInputWidget = ({
  report,
  identifier,
  type,
}: {
  report: Report
  identifier: string
  type: 'one-time' | 'recurring'
}) => {
  cy.findByTestId(
    `ReportForm-${
      type === 'one-time' ? 'OneTime' : 'Recurring'
    }-NumberType-${identifier}`,
  )
    .should('exist')
    .should(
      'contain.text',
      report.prompts?.find((i) => i.identifier === identifier)?.name,
    )
}

export const fillNumberInputWidget = ({
  identifier,
  type,
}: {
  identifier: string
  type: 'one-time' | 'recurring'
}) => {
  cy.findByTestId(
    `ReportForm-${
      type === 'one-time' ? 'OneTime' : 'Recurring'
    }-NumberType-${identifier}`,
  )
    .find('input')
    .type(numberValue)
}

export const checkDriverAndGroupWidget = (report: Report) => {
  const prompt = report.prompts?.find((i) =>
    // @ts-expect-error identifier
    PROMPT_KNOWN_DRIVER_TYPE_FIELD_NAME.includes(i.identifier),
  )

  cy.findByTestId('ReportForm-DriversAndGroups')
    .should('exist')
    .should('contain.text', prompt?.name)

  cy.findByTestId('ReportForm-DriversAndGroups')
    .find('input')
    .should('have.value', prompt?.type === 'DRIVERLIST' ? 'All Drivers' : '')
}

export const fillDriverAndGroupWidget = (value: string) => {
  cy.findByTestId('ReportForm-DriversAndGroups').find('input').click()

  cy.findByRole('option', {
    name: value,
  }).click()
}

export const fillTimeWidgetByType = ({
  value: { time },
  identifier,
  name,
}: {
  name: string
  identifier: string
  value: { time: string }
}) => {
  cy.findByTestId(`ReportDrawer-Form-Time-${identifier}`).should('contain.text', name)

  cy.findByTestId(`ReportDrawer-Form-Time-${identifier}`)
    .find('[contenteditable="false"]')
    .type(time)
}

export const fillTimeWidgetByClick = ({
  value: { hour, minute },
  name,
  identifier,
}: {
  name: string
  identifier: string
  value: { hour: string; minute: string }
}) => {
  cy.findByTestId(`ReportDrawer-Form-Time-${identifier}`).should('contain.text', name)

  cy.findByTestId(`ReportDrawer-Form-Time-${identifier}`).find('button').click()
  cy.get(`[aria-label="${Number.parseInt(hour)} hours"]`).click({ force: true })
  cy.get(`[aria-label="${Number.parseInt(minute)} minutes"]`).click({ force: true })
  cy.get('.MuiPickersLayout-actionBar').find('button').last().click({ force: true })
}

export const checkStartDateOrStartDateTimeWidget = (report: Report) => {
  const isDateTime = report.prompts?.some((p) => p.type.includes('DATETIME'))
  const testid = isDateTime ? 'ReportForm-StartDateTime' : 'ReportForm-StartDate'

  cy.findByTestId(testid).should('exist')
}

export const fillStartDateTimeWidget = () => {
  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiPickersSectionList-root')
    .type(`${type5Left}${validDateTimeRangeInputString.start}`)

  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiFormHelperText-root')
    .should('not.exist')
}

export const fillInvalidStartDateTimeWidget = () => {
  cy.log('__******__ __fillInvalidStartDateTimeWidget__ __******__')
  // before min date
  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiPickersSectionList-root')
    .type(`${type5Left}199912121212`)

  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Start Date must not be exceed min date')

  // after max date
  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiPickersSectionList-root')
    .type(`${type5Left}299912121212`)

  cy.findByTestId('ReportForm-StartDateTime')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'End Date must not be exceed max date')
}

export const fillStartDateWidget = () => {
  cy.findByTestId('ReportForm-StartDate')
    .find('.MuiPickersSectionList-root')
    .type(`${type3Left}${validDateRangeStringValue.start}`)
}

export const fillInvalidStartDateWidget = () => {
  cy.log('__******__ __fillInvalidStartDateWidget__ __******__')
  // before min date
  cy.findByTestId('ReportForm-StartDate')
    .find('.MuiPickersSectionList-root')
    .type(`${type3Left}19991212`)

  cy.findByTestId('ReportForm-StartDate')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'Start Date must not be exceed min date')

  // after max date
  cy.findByTestId('ReportForm-StartDate')
    .find('.MuiPickersSectionList-root')
    .type(`${type3Left}29991212`)

  cy.findByTestId('ReportForm-StartDate')
    .find('.MuiFormHelperText-root')
    .should('contain.text', 'End Date must not be exceed max date')
}

export const checkSingleSelectWidget = ({
  report,
  identifier,
}: {
  report: Report
  identifier: string
}) => {
  cy.log('__******__ __checkSingleSelectWidget__ __******__')
  cy.findByTestId(`ReportForm-SingleSelect-${identifier}`)
    .should('exist')
    .should(
      'contain.text',
      report.prompts?.find((i) => i.identifier === identifier)?.name,
    )

  cy.findByTestId(`ReportForm-SingleSelect-${identifier}`)
    .find('input')
    .should('have.value', '')
}

export const testFileFormats = (report: Report) => {
  // File format is rendered based on report file_formats
  cy.findByTestId('ReportForm-FileFormat')
    .should('contain.text', 'File Format')
    .as('fileFormatField')
  cy.get('@fileFormatField')
    .find('input')
    .should('have.lengthOf', report.file_formats.length)

  // PDF format is selected by default
  cy.contains('Adobe Acrobat Reader (pdf)').find('input').should('have.attr', 'checked')
}

export const testEmailSection = ({
  report,
  type,
}: {
  report: Report
  type: 'one-time' | 'recurring'
}) => {
  if (type === 'one-time') {
    if (report.delivery_types?.includes('Web Link')) {
      // Downloadable report

      // Switch send email
      cy.findByTestId('ReportForm-SendDate').should('not.exist')
      cy.findByTestId('ReportForm-EmailSection').should('not.exist')
      cy.findByTestId('ReportForm-PasswordField').should('not.exist')

      cy.findByTestId('ReportForm-SendEmailSwitch').find('input').check()

      cy.findByTestId('ReportForm-SendDate').should('exist')
      cy.findByTestId('ReportForm-EmailSection').should('exist')
      cy.findByTestId('ReportForm-PasswordField').should('exist')
    } else {
      // Undownloadable report

      // Send email switch should be disabled
      cy.findByTestId('ReportForm-SendEmailSwitch')
        .find('input')
        .should('be.disabled')
        .should('be.checked')
      cy.findByTestId('ReportForm-SendDate').should('exist')
      cy.findByTestId('ReportForm-EmailSection').should('exist')
      cy.findByTestId('ReportForm-PasswordField').should('exist')
    }
  }

  // Email list should contain user email by default
  cy.findByTestId('ReportForm-EmailSection')
    .find('ul')
    .find('li')
    .should('have.text', mockState.settings.primaryEmail)
    .should('have.lengthOf', 1)

  // Add email input rendering
  cy.findByTestId('ReportForm-AddEmailInput').should('not.exist')
  cy.contains('Add more receivers').click()
  cy.findByTestId('ReportForm-AddEmailInput').should('exist')

  // Add email input validation
  cy.findByTestId('ReportForm-AddEmailInput')
    .find('button')
    .contains(/Add/i)
    .as('addEmailButton')
    .should('be.disabled')

  cy.findByTestId('ReportForm-AddEmailInput').find('input').type(newInvalidEmail)
  cy.findByTestId('ReportForm-AddEmailInput')
    .find('p.MuiFormHelperText-root')
    .as('addEmailHelperText')
    .contains(/Please provide a valid email/i)
  cy.get('@addEmailButton').should('be.disabled')
  cy.findByTestId('ReportForm-AddEmailInput').find('input').clear()
  cy.findByTestId('ReportForm-AddEmailInput').find('input').type(newValidEmail)
  cy.get('@addEmailHelperText').should('not.exist')

  cy.get('@addEmailButton').should('not.be.disabled').click()

  // Check if new email is added
  cy.findByTestId('ReportForm-EmailSection')
    .find('ul')
    .find('li')
    .should('have.lengthOf', 2)
    .last()
    .should('have.text', newValidEmail)
  cy.get('@addEmailButton').should('be.disabled')
  cy.findByTestId('ReportForm-AddEmailInput').find('input').should('have.value', '')

  // Password field rendering
  cy.findByTestId('ReportForm-PasswordInput').should('not.exist')
  cy.findByTestId('ReportForm-PasswordField').find('input[type="checkbox"]').check()
  cy.findByTestId('ReportForm-PasswordInput').should('exist').type(passwordText)
}

export const fillOneTimeEmailSection = () => {
  cy.findByTestId('ReportForm-SendEmailSwitch').find('input').check()
}

export const submitReportAndClose = () => {
  cy.findByTestId('ReportDrawer-SaveButton').click()

  cy.wait('@ct_fleet_submit_client_report_v3')
  // After submit drawee should remaining open
  cy.findByTestId('Report-ExportDrawer').should('exist')

  testCloseExportDrawer()

  return cy.findByTestId('Report-ExportDrawer').should('not.exist')
}

export const checkSelectRecurringForm = () => {
  cy.findByTestId('Report-RecurringButton').click()
  cy.findByTestId('Report-RecurringExportForm').should('exist')
}

export const checkRepeatIntervalAndSendDateAndSummary = () => {
  cy.log(
    '__************__ __REPEAT INTERVAL AND SEND DATA AND SUMMARY__ __************__',
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunInterval').as(
    'firstRun',
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunInterval').as(
    'secondRun',
  )
  cy.findByTestId('ReportForm-Recurring-ReportFrequency').should('exist')

  // Check if default value is Daily
  cy.findByTestId('ReportForm-Recurring-ReportFrequency')
    .find('input')
    .should('have.value', ReportFrequencyOptions.DAILY)
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

  // Check if default send date is today
  cy.findByTestId('ReportForm-SendDate').should(
    'contain.text',
    DateTime.local().toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY),
  )

  // Check amnount of Repeat interval options
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByRole('listbox')
    .find('li')
    .should('have.lengthOf', Object.keys(ReportFrequencyOptions).length)

  // Test interval summary for Daily option
  testIntervalSummaryDaily()

  // Test Weekly option
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Weekly').click()

  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('not.exist')
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek').should('exist')

  // Check if days of week is mandatory
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek')
    .find('label')
    .as('daysOfWeekOptions')
    .should('have.lengthOf', 7)

  // check the monday should show the summary
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek-1').find('input').check()
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

  // uncheck
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek-1').find('input').uncheck()
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek').should(
    'contain.text',
    'You need to select at least one day',
  )

  // Test interval summary for Weekly option
  testIntervalSummaryWeekly()

  // Test Monthly option
  cy.findByTestId('ReportForm-Recurring-DayOfMonth').should('not.exist')
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Monthly').click()
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')
  cy.findByTestId('ReportForm-Recurring-DayOfMonth').should('exist')

  // check if option '1st day of the month' is selected by default
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-First')
    .children()
    .last()
    .should('contain.text', '1st day of the month')

  cy.findByTestId('ReportForm-Recurring-DayOfMonth-First')
    .find('input')
    .should('be.checked')
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

  // Check if send date is set to the next first day of month
  const nextFirstDayOfMonth = getNextDayOfWeekOrMonth('1', 'month')
  cy.findByTestId('ReportForm-SendDate').should(
    'contain.text',
    nextFirstDayOfMonth?.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY),
  )

  // Select 'Last day of the month' option
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Last')
    .children()
    .last()
    .should('contain.text', 'Last day of the month')
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Last').find('input').check()
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

  // Check if send date is set to the next last day of month
  const nextLastDayOfMonth = DateTime.local().endOf('month')
  cy.findByTestId('ReportForm-SendDate').should(
    'contain.text',
    nextLastDayOfMonth?.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY),
  )

  // Cutom day of month input should be disabled when 'Select any other days' option is not selected
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-Select')
    .find('input')
    .should('be.disabled')
    .should('have.value', '')

  // Select 'Select any other days' option
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom')
    .children()
    .last()
    .should('contain.text', 'Select any other days')

  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom').find('input').check()
  // summary should hide to wait the custom value
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('not.exist')

  cy.findByTestId('ReportForm-Recurring-DayOfMonth').should(
    'contain.text',
    messages.required,
  )

  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-Select').click()

  cy.findByRole('listbox').find('li').should('have.lengthOf', 30) // 30 days in month (does not include 1st)
  cy.findByRole('option', {
    name: /15/i,
  }).click()
  cy.findByTestId('ReportForm-Recurring-IntervalSummary').should('exist')

  // Check if send date is set to selected day of month
  const nextSelectedDayOfMonth = getNextDayOfWeekOrMonth('15', 'month')
  cy.findByTestId('ReportForm-SendDate').should(
    'contain.text',
    nextSelectedDayOfMonth?.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY),
  )

  cy.findByTestId('ReportForm-Recurring-DayOfMonth').should(
    'not.contain.text',
    messages.required,
  )

  // Test interval summary for Monthly option
  testIntervalSummaryMonthly()

  // Test interval summary for Custom option
  testIntervalSummaryCustom()
}

export const fillIntervalSelection = (
  value: 'Daily' | 'Weekly' | 'Monthly' | 'Custom interval',
) => {
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByTestId(`ReportForm-Recurring-ReportFrequency-${value}`).click()
}

export const checkDataDuration = () => {
  cy.findByTestId('ReportForm-Recurring-DataDuration').should('exist')

  // set interval to daily to test duration from beginning
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Daily').click()

  // Check if default value is Previous day
  cy.findByTestId('ReportForm-Recurring-DataDuration')
    .find('input')
    .should('have.value', DataToReceiveDurationOptions.PREVIOUS_DAY)

  // Check amount of Data duration options
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('listbox')
    .find('li')
    .should('have.lengthOf', Object.keys(DataToReceiveDurationOptions).length)

  // Select custom duration
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount').should('not.exist')
  cy.findByRole('option', {
    name: /Custom duration/i,
  }).click()
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount').should('exist')

  // Check if default value of Custom duration is 2 Days
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount')
    .find('input')
    .should('have.value', '2')
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit')
    .find('input')
    .should('have.value', DataToReceiveCustomPeriodOptions.DAYS)

  // Check if custom duration is mandatory
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount')
    .find('input')
    .clear()
  cy.findByTestId('ReportForm-Recurring-DataDuration-HelperText').should(
    'contain.text',
    messages.required,
  )

  // Check amount of Custom duration unit options
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit').click()
  cy.findByRole('listbox')
    .find('li')
    .should('have.lengthOf', Object.keys(DataToReceiveCustomPeriodOptions).length)
  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit-Weeks').click()

  cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount').type('1')
}

export const fillDataDurationValue = (
  value:
    | 'Previous day'
    | 'Previous 7 days'
    | 'Previous 30 days'
    | 'Previous month'
    | 'Beginning of the month till date'
    | 'Custom duration',
) => {
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByTestId(`ReportForm-Recurring-DataDuration-${value}`).click()
}

export const testCloseExportDrawer = () => {
  cy.findByTestId('ReportDrawer-CancelButton').click()
  return cy.findByTestId('Report-ExportDrawer').should('not.exist')
}

const testSummaryIntervalDateFormat = (firstRun: DateTime, secondRun: DateTime) => {
  const isFirstRunToday = firstRun.hasSame(DateTime.local(), 'day')

  cy.get('@firstRun').should(
    'contain.text',
    firstRun.toFormat(`cccc${isFirstRunToday ? ` '(Today)'` : ''} - dd MMM y`),
  )
  cy.get('@secondRun').should('contain.text', secondRun.toFormat(`cccc - dd MMM y`))
}

const testIntervalSummaryDaily = () => {
  cy.findByRole('option', {
    name: /Daily/,
  }).click()
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-Title').should(
    'contain.text',
    'The report will be sent daily',
  )

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY DAILY__ __************__ Test case 1: Send date is today',
  )
  // Test case 1: Send date is today
  // First run should be today
  // Second run should be tomorrow
  const firstRun1 = DateTime.local()
  const secondRun1 = firstRun1.plus({ days: 1 })
  testSummaryIntervalDateFormat(firstRun1, secondRun1)
  testDataDurationSummary(firstRun1, secondRun1)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY DAILY__ __************__ Test case 2: Send date is in the future',
  )
  // Test case 2: Send date is in the future
  cy.findByTestId('ReportForm-SendDateInput').click()
  // select first day of the following month
  cy.get('button[aria-label="Next month"]').click()

  cy.get('.MuiDayCalendar-weekContainer[aria-rowindex="1"]') // first week
    .findByText('1')
    .click()
  // click outside to close calendar
  cy.findByTestId('Report-ExportReportName').click()
  // First run should be the first day of next month
  // Second run should be the next day of the first run
  const firstRun2 = DateTime.local().plus({ month: 1 }).set({ day: 1 })
  const secondRun2 = firstRun2.plus({ days: 1 })
  testSummaryIntervalDateFormat(firstRun2, secondRun2)
  testDataDurationSummary(firstRun2, secondRun2)
}

const testIntervalSummaryWeekly = () => {
  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY WEEKLY__ __************__ Test case 1:Select one day in week',
  )
  // Test case 1: Select one day in week
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek-1').click() // select Monday
  // First run should be the closest Monday (today or in the future)
  // Second run should be one week after the first run
  const firstRun1 = getNextDayOfWeekOrMonth('1', 'week') as DateTime
  const secondRun1 = firstRun1.plus({ weeks: 1 })
  testSummaryIntervalDateFormat(firstRun1, secondRun1)
  testDataDurationSummary(firstRun1, secondRun1)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY WEEKLY__ __************__ Test case 2: Select two days in week',
  )
  // Test case 2: Select two days in week
  // Monday was already selected
  cy.findByTestId('ReportForm-Recurring-DaysOfWeek-3').click() // select Wednesday
  // First run should be either Monday or Wednesday (whichever closer in the future)
  // Second run should be the next Monday or Wednesday after the first run
  const todayDOW = DateTime.local().toFormat('c')
  const firstRunDOW = Number(todayDOW) === 1 || Number(todayDOW) > 3 ? '1' : '3'
  const firstRun2 = getNextDayOfWeekOrMonth(firstRunDOW, 'week') as DateTime
  const isSecondRunOnFollowingWeek = firstRunDOW === '3'
  const secondRun2 = firstRun2.plus({
    days: isSecondRunOnFollowingWeek ? 7 - 2 : 2,
  })
  testSummaryIntervalDateFormat(firstRun2, secondRun2)
  testDataDurationSummary(firstRun2, secondRun2)
}

const testIntervalSummaryMonthly = () => {
  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY MONTHLY__ __************__ Test case 1:Select first day of the month',
  )
  // Test case 1: Select first day of the month
  cy.log('')
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-First').find('input').check()
  const firstRun1 = getNextDayOfWeekOrMonth('1', 'month') as DateTime
  const secondRun1 = firstRun1.plus({ months: 1 })
  testSummaryIntervalDateFormat(firstRun1, secondRun1)
  testDataDurationSummary(firstRun1, secondRun1)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY MONTHLY__ __************__ Test case 2: Select last day of the month',
  )
  // Test case 2: Select last day of the month
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Last').find('input').check()
  const firstRun2 = DateTime.local().endOf('month')
  const secondRun2 = firstRun2.plus({ months: 1 })
  testSummaryIntervalDateFormat(firstRun2, secondRun2)
  testDataDurationSummary(firstRun2, secondRun2)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY MONTHLY__ __************__ Test case 3: Select a custom day of the month',
  )
  // Test case 3: Select a custom day of the month
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom').find('input').check()
  // Select 15th day of the month
  cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-Select').click()
  cy.findByRole('option', {
    name: /15/i,
  }).click()
  const firstRun3 = getNextDayOfWeekOrMonth('15', 'month') as DateTime
  const secondRun3 = firstRun3.plus({ months: 1 })
  testSummaryIntervalDateFormat(firstRun3, secondRun3)
  testDataDurationSummary(firstRun3, secondRun3)
}

const testIntervalSummaryCustom = () => {
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Selection').click()
  cy.findByTestId('ReportForm-Recurring-ReportFrequency-Custom interval').click()

  // Select send date as today
  cy.findByTestId('ReportForm-SendDateInput').click()
  // Try to select Today on date picker, even if the month opened initially is the next month (where today is not available)
  cy.tryGetOrElse('[aria-current="date"]', {
    else: ({ failedSelector }) => {
      cy.findByTestId('ArrowLeftIcon').click()
      return cy.get(failedSelector)
    },
  }).click()

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY CUSTOM__ __************__ Test case 1: every x days',
  )
  // Test case 1: every x days
  cy.findByTestId('ReportForm-Recurring-Custom-Frequency').type('{selectAll}{del}3') // every 3 days
  const firstRun1 = DateTime.local()
  const secondRun1 = firstRun1.plus({ days: 3 })
  testSummaryIntervalDateFormat(firstRun1, secondRun1)
  testDataDurationSummary(firstRun1, secondRun1)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY CUSTOM__ __************__ Test case 2: every x weeks',
  )
  // Test case 2: every x weeks
  cy.findByTestId('ReportForm-Recurring-Custom-Interval').click()
  cy.findByRole('option', {
    name: /Weeks/i,
  }).click()
  cy.findByTestId('ReportForm-Recurring-Custom-Frequency').type('{selectAll}{del}4') // every 4 weeks
  const firstRun2 = DateTime.local()
  const secondRun2 = firstRun1.plus({ weeks: 4 })
  testSummaryIntervalDateFormat(firstRun2, secondRun2)
  testDataDurationSummary(firstRun2, secondRun2)

  cy.log(
    '__************__ __CHECK INTERVAL SUMMARY CUSTOM__ __************__ Test case 3: every x months',
  )
  cy.findByTestId('ReportForm-Recurring-Custom-Interval').click()
  cy.findByRole('option', {
    name: /Months/i,
  }).click()
  cy.findByTestId('ReportForm-Recurring-Custom-Frequency').type('{selectAll}{del}2') // every 2 months
  const firstRun3 = DateTime.local()
  const secondRun3 = firstRun1.plus({ months: 2 })
  testSummaryIntervalDateFormat(firstRun3, secondRun3)
  testDataDurationSummary(firstRun3, secondRun3)
}

const testDataDurationSummary = (firstRun: DateTime, secondRun: DateTime) => {
  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 1: Previous day',
  )
  // Test case 1: Previous day
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Previous day/i,
  }).click()
  const firstRunDuration1 = firstRun.minus({ day: 1 })
  const secondRunDuration1 = secondRun.minus({ day: 1 })
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export of ${firstRunDuration1.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export of ${secondRunDuration1.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )}`,
  )

  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 2: Previous 7 days',
  )
  // Test case 2: Previous 7 days
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Previous 7 days/i,
  }).click()
  const firstRunDuration2Start = firstRun.minus({ days: 7 })
  const firstRunDuration2End = firstRun.minus({ day: 1 })
  const secondRunDuration2Start = secondRun.minus({ days: 7 })
  const secondRunDuration2End = secondRun.minus({ day: 1 })
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export from ${firstRunDuration2Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${firstRunDuration2End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export from ${secondRunDuration2Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${secondRunDuration2End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )

  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 3: Previous 30 days',
  )
  // Test case 3: Previous 30 days
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Previous 30 days/i,
  }).click()
  const firstRunDuration3Start = firstRun.minus({ days: 30 })
  const firstRunDuration3End = firstRun.minus({ day: 1 })
  const secondRunDuration3Start = secondRun.minus({ days: 30 })
  const secondRunDuration3End = secondRun.minus({ day: 1 })
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export from ${firstRunDuration3Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${firstRunDuration3End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export from ${secondRunDuration3Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${secondRunDuration3End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )

  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 4: Previous month',
  )
  // Test case 4: Previous month
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Previous month/i,
  }).click()
  const firstRunDuration4Start = firstRun.minus({ month: 1 }).startOf('month')
  const firstRunDuration4End = firstRun.minus({ month: 1 }).endOf('month')
  const secondRunDuration4Start = secondRun.minus({ month: 1 }).startOf('month')
  const secondRunDuration4End = secondRun.minus({ month: 1 }).endOf('month')
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export from ${firstRunDuration4Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${firstRunDuration4End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export from ${secondRunDuration4Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${secondRunDuration4End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )

  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 5: One month ago till date',
  )
  // Test case 5: One month ago till date
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /One month ago till date/i,
  }).click()
  const firstRunDuration5End = firstRun.minus({ day: 1 })
  const firstRunDuration5Start = firstRunDuration5End.minus({ month: 1 })

  const secondRunDuration5End = secondRun.minus({ day: 1 })
  const secondRunDuration5Start = secondRunDuration5End.minus({ month: 1 })
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export from ${firstRunDuration5Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${firstRunDuration5End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export from ${secondRunDuration5Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${secondRunDuration5End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )

  cy.log(
    '__************__ __CHECK DATA DURATION SUMMARY __************__ Test case 6: Beginning of the month till date',
  )
  // Test case 6: Beginning of the month till date
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Beginning of the month till date/i,
  }).click()
  const firstRunDuration6Start = firstRun.startOf('month')
  const firstRunDuration6End = firstRunDuration6Start.hasSame(firstRun, 'day')
    ? firstRun
    : firstRun.minus({ day: 1 })
  const secondRunDuration6Start = secondRun.startOf('month').startOf('month')
  const secondRunDuration6End = secondRunDuration6Start.hasSame(secondRun, 'day')
    ? secondRun
    : secondRun.minus({ day: 1 })
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-FirstRunDuration').should(
    'have.text',
    `Data export from ${firstRunDuration6Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${firstRunDuration6End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )
  cy.findByTestId('ReportForm-Recurring-IntervalSummary-SecondRunDuration').should(
    'have.text',
    `Data export from ${secondRunDuration6Start.toLocaleString(
      DateTime.DATE_MED_WITH_WEEKDAY,
    )} to ${secondRunDuration6End.toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY)}`,
  )

  // Change back to default option
  cy.findByTestId('ReportForm-Recurring-DataDuration-Selection').click()
  cy.findByRole('option', {
    name: /Previous day/i,
  }).click()
}
