/* eslint-disable no-param-reassign */
import type { CyHttpMessages } from 'cypress/types/net-stubbing'
import { createMemoryHistory } from 'history'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import type {
  ExportReportNewApi,
  ReportPromptValueTypeApiInput,
} from 'api/reports/types'
import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import {
  geofenceEndpointMocks,
  geofenceGroupsMock,
  geofencesMock,
} from 'src/cypress-ct/mocks/endpoints/geofence'
import {
  allReports,
  driverGroupsMock,
  driversMock,
  reportEndpointMocks,
  timezoneToParseDates,
  vehicleGroupsMock,
  vehiclesMock,
} from 'src/cypress-ct/mocks/endpoints/report'
import {
  cyExpect,
  exhaustiveEndpointCallCheck,
  matchWithMethod,
  mountWithProviders,
  runTestsInOrderWithLoggingAndSetup,
} from 'src/cypress-ct/utils'

import ReportRoute from '../index'
import { DataToReceiveDurationOptions } from './util'
import {
  checkDataDuration,
  checkDateRangePrompt,
  checkDateRangeShortcuts,
  checkDateTimeRangePrompt,
  checkDriverAndGroupWidget,
  checkGeofenceGroupPrompt,
  checkGeofencePrompt,
  checkNumberInputWidget,
  checkRegistrationMultiplePromptWidget,
  checkRegistrationPromptValue,
  checkRepeatIntervalAndSendDateAndSummary,
  checkSelectRecurringForm,
  checkSingleSelectWidget,
  checkStartDateOrStartDateTimeWidget,
  checkTextInputPrompt,
  dummyText,
  fillDataDurationValue,
  fillDriverAndGroupWidget,
  fillGeofenceGroupWidget,
  fillGeofenceWidget,
  fillIntervalSelection,
  fillInvalidDateRange,
  fillInvalidDateTimeRangePrompt,
  fillInvalidStartDateTimeWidget,
  fillInvalidStartDateWidget,
  fillNumberInputWidget,
  fillRegistrationMultipleWidget,
  fillRegistrationPromptSingleValue,
  fillStartDateTimeWidget,
  fillStartDateWidget,
  fillTextInputPrompt,
  fillTimeWidgetByClick,
  fillTimeWidgetByType,
  fillValidDateRange,
  fillValidDateTimeRangePrompt,
  markDateRangeStartEndComponent,
  markDateTimeRangePrompt,
  newValidEmail,
  numberValue,
  passwordText,
  submitReportAndClose,
  testCloseExportDrawer,
  testEmailSection,
  testFileFormats,
  testSelectReport,
  fillOneTimeEmailSection as turnOnOneTimeEmailSection,
  validDateRange,
  validDateTimeRangeInput,
} from './util.cypress'

const basePath = '/reports'

const globalHistory = createMemoryHistory({
  initialEntries: [basePath],
})

const defaultFileFormat = 'pdf'
const defaultStartTimeHour = '05'
const defaultStartTimeMinute = '45'
const defaultEndTime = '18:32'
const defaultDuration = '00:07'
const defaultUpDown = 'DOWN'

const mountAllReportsMain = () => {
  mountWithProviders(<ReportRoute />, {
    history: globalHistory,
    reduxOptions: {
      preloadedState: {
        reports: duxsMocks.reports,
        user: duxsMocks.user({ defaultTimezone: timezoneToParseDates }).mockState,
        vehicles: duxsMocks.vehicles({
          vehicles: vehiclesMock,
          vehicleGroups: vehicleGroupsMock,
        }),
        drivers: duxsMocks.drivers({
          drivers: driversMock,
          driverGroups: driverGroupsMock,
        }),
      },
    },
  })
}

const handleApiCalls = (req: CyHttpMessages.IncomingHttpRequest) =>
  match(req.body)
    .with({ method: 'ct_fleet_get_report_preview' }, () => {
      req.reply({ fixture: 'dummy.pdf' })
    })
    .with(matchWithMethod(req, 'ct_fleet_get_report_options_v2'), () => {
      req.reply(reportEndpointMocks.ct_fleet_get_report_options_v2())
    })
    .with({ method: 'ct_fleet_get_favourite_reports' }, () => {
      req.alias = 'getFavouriteReports'
      req.reply(reportEndpointMocks.ct_fleet_get_favourite_reports())
    })
    .with({ method: 'ct_fleet_get_customized_reports' }, () => {
      req.reply(reportEndpointMocks.ct_fleet_get_customized_reports())
    })
    .with({ method: 'ct_fleet_get_custom_report_resources' }, () => {
      req.reply(reportEndpointMocks.ct_fleet_get_custom_report_resources())
    })
    .with({ method: 'ct_fleet_get_geofence_v2' }, () => {
      req.reply(geofenceEndpointMocks.ct_fleet_get_geofence_v2())
    })
    .with({ method: 'ct_fleet_get_prelogin_data' }, () => {
      req.reply({ delay: 50, body: { id: 10, result: {} } })
    })
    .with({ method: 'ct_fleet_get_report_profile_data' }, () => {
      req.reply(reportEndpointMocks.ct_fleet_get_report_profile_data())
    })
    .otherwise(exhaustiveEndpointCallCheck)

describe('Export Drawer for OneTime Form', () => {
  const setup = () => {
    // NOTE: for the first case, wait to load the multiple requests
    mountAllReportsMain()
    cy.wait('@ct_fleet_get_report_options_v2', { requestTimeout: 10000 })
    cy.wait('@getFavouriteReports', { requestTimeout: 10000 })
  }

  runTestsInOrderWithLoggingAndSetup('passes several types of report forms', {
    setupIfFirstTest: setup,
    tests: [
      {
        name: 'Open drawer for report including dateRange with Date, geofence, desc, fill multiple emails, password',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[0]
          const submittedReport = {
            id: selectedReport.report_id,
            prompts: [
              {
                type: 'DATE',
                identifier: 'start_date',
                value: `${validDateRange.start.toFormat('yyyy-LL-dd')} 00:00:00`,
              },
              {
                type: 'DATE',
                identifier: 'end_date',
                value: `${validDateRange.end.toFormat('yyyy-LL-dd')} 23:59:59`,
              },
              {
                type: 'GEOFENCE',
                identifier: 'geofence_id',
                value: geofencesMock['2b'].geofence_id,
              },
              {
                type: 'DESC',
                identifier: 'km_cost',
                value: dummyText,
              },
              {
                type: 'FREE_TEXT',
                identifier: 'rest_days',
                value: '12,23',
              },
              {
                type: 'VEHLIST',
                identifier: 'Vehicle Name',
                value: 'all',
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: `${
              duxsMocks.user({}).mockState.settings.primaryEmail
            };${newValidEmail}`,
          }

          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.id).toEqual(submittedReport.id)
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.password).toEqual(passwordText)
                  cyExpect(params.zipped).toEqual(true)
                  cyExpect(params.schedule).toEqual(undefined)
                  cyExpect(params.schedule_end).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.prompts).toDeepEqual(submittedReport.prompts)

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // One-time form is displayed
          cy.findByTestId('Report-OneTimeExportForm').should('exist')
          cy.findByTestId('Report-RecurringExportForm').should('not.exist')

          // check default vehicle is all
          checkRegistrationPromptValue('All Vehicles')

          // date range
          markDateRangeStartEndComponent()
          checkDateRangePrompt()
          checkDateRangeShortcuts()
          fillInvalidDateRange()
          fillValidDateRange()

          // geofence
          checkGeofencePrompt(selectedReport)
          fillGeofenceWidget(geofencesMock['2b'].geofence_name)

          // string type
          checkTextInputPrompt({
            report: selectedReport,
            identifier: 'km_cost',
            type: 'one-time',
          })
          fillTextInputPrompt({
            identifier: 'km_cost',
            type: 'one-time',
          })
          fillTextInputPrompt({
            identifier: 'rest_days',
            type: 'one-time',
            typeText: '12,23',
          })

          testFileFormats(selectedReport)
          testEmailSection({
            report: selectedReport,
            type: 'one-time',
          })

          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 2 No download, dateRange with DateTime, vehicle no all with group value, geofence group, username, only one file format',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[1]
          const submittedReport = {
            id: selectedReport.report_id,
            schedule: false,
            prompts: [
              {
                type: 'DATETIME',
                identifier: 'start_date',
                value: `${validDateTimeRangeInput.start.toFormat(
                  'yyyy-LL-dd HH:mm:00ZZZ',
                )}`,
              },
              {
                type: 'DATETIME',
                identifier: 'end_date',
                value: `${validDateTimeRangeInput.end.toFormat(
                  'yyyy-LL-dd HH:mm:00ZZZ',
                )}`,
              },
              {
                type: 'GEOFENCEGROUP',
                identifier: 'geofence_group',
                value: geofenceGroupsMock['323'].group_geofence_id,
              },
              {
                type: 'USER_NAME',
                identifier: 'username',
                value: dummyText,
              },
              {
                type: 'VEHLIST_NOALL',
                identifier: 'Vehicle Name',
                value: `g-${vehicleGroupsMock[0].id}`,
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: duxsMocks.user({}).mockState.settings.primaryEmail,
          }

          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.password).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.zipped).toEqual(undefined)
                  // eslint-disable-next-line
                  // eslint-disable-next-line unicorn/no-array-for-each
                  params.prompts.forEach((prompt, i) => {
                    cyExpect(prompt, `prompt: ${prompt.identifier}`).toDeepEqual(
                      submittedReport.prompts[i],
                    )
                  })

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // no all single, with group value
          fillRegistrationPromptSingleValue({
            value: vehicleGroupsMock[0].name,
            includeAll: false,
          })

          // date time range
          markDateTimeRangePrompt()
          checkDateTimeRangePrompt()
          fillInvalidDateTimeRangePrompt()
          fillValidDateTimeRangePrompt()

          // geofence group
          checkGeofenceGroupPrompt(selectedReport)
          fillGeofenceGroupWidget()

          // have tested, fill only here
          fillTextInputPrompt({
            identifier: 'username',
            type: 'one-time',
          })

          // only one file format
          cy.findByTestId('ReportForm-FileFormat')
            .children()
            .last()
            .should('have.text', selectedReport.file_formats[0].description)

          // no radio input
          cy.findByTestId('ReportForm-FileFormat').find('input').should('not.exist')
          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 3, with Driver group, start/end time, vehicle no all no group, number',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[2]
          const submittedReport = {
            id: selectedReport.report_id,
            schedule: false,
            prompts: [
              {
                type: 'DATE',
                identifier: 'start_date',
                value: `${validDateRange.start.toFormat('yyyy-LL-dd')} 00:00:00`,
              },
              {
                type: 'DATE',
                identifier: 'end_date',
                value: `${validDateRange.end.toFormat('yyyy-LL-dd')} 23:59:59`,
              },
              {
                type: 'NUMBER',
                identifier: 'in_interval',
                value: numberValue,
              },
              {
                type: 'TIME-S',
                identifier: 'working_hours_start',
                value: `${defaultStartTimeHour}:${defaultStartTimeMinute}`,
              },
              {
                type: 'TIME',
                identifier: 'working_hours_end',
                value: defaultEndTime,
              },
              {
                type: 'VEHLIST_NOGROUPS_NOALL',
                identifier: 'Vehicle Name',
                value: vehiclesMock[0].id,
              },
              {
                type: 'DRIVERLIST',
                identifier: 'driver_name',
                value: driversMock[0].id,
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: duxsMocks.user({}).mockState.settings.primaryEmail,
          }

          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.zipped).toEqual(undefined)
                  cyExpect(params.password).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.prompts).toDeepEqual(submittedReport.prompts)

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // just fill the date range widgets
          markDateRangeStartEndComponent()
          fillValidDateRange()
          fillRegistrationPromptSingleValue({
            value: vehiclesMock[0].name,
            includeAll: false,
          })

          // driver
          checkDriverAndGroupWidget(selectedReport)
          fillDriverAndGroupWidget(driversMock[0].name)

          // start time
          fillTimeWidgetByClick({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.identifier ?? '',
            value: { hour: defaultStartTimeHour, minute: defaultStartTimeMinute },
          })

          // end time
          fillTimeWidgetByType({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('end'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('end'),
              )?.identifier ?? '',
            value: { time: defaultEndTime },
          })

          // number
          checkNumberInputWidget({
            report: selectedReport,
            identifier: 'in_interval',
            type: 'one-time',
          })
          fillNumberInputWidget({
            identifier: 'in_interval',
            type: 'one-time',
          })

          turnOnOneTimeEmailSection()

          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 4, vehicle multiple, startDate (DATE-S), driver no all, timefine',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[3]
          const submittedReport = {
            id: selectedReport.report_id,
            schedule: false,
            prompts: [
              {
                type: 'DATE-S',
                identifier: 'start_date',
                value: `${validDateRange.start.toFormat('yyyy-LL-dd')} 00:00:00`,
              },
              {
                type: 'TIMEFINE',
                identifier: 'duration',
                value: defaultDuration,
              },
              {
                identifier: 'start_time',
                type: 'TIME',
                value: `${defaultStartTimeHour}:${defaultStartTimeMinute}`,
              },
              {
                type: 'VEHLISTDETAIL',
                identifier: 'Vehicle Name',
                value: [
                  { vehicle_id: vehiclesMock[0].id },
                  { group_id: `g-${vehicleGroupsMock[0].id}` },
                ],
              },
              {
                type: 'DRIVERLIST_NOALL',
                identifier: 'driver_name',
                value: driversMock[0].id,
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: duxsMocks.user({}).mockState.settings.primaryEmail,
          }
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.zipped).toEqual(undefined)
                  cyExpect(params.password).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.prompts).toDeepEqual(submittedReport.prompts)

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // vehicle multiple values
          checkRegistrationMultiplePromptWidget()
          fillRegistrationMultipleWidget({
            vehicle: vehiclesMock[0].name,
            group: vehicleGroupsMock[0].name,
          })

          // fill driver
          fillDriverAndGroupWidget(driversMock[0].name)
          checkStartDateOrStartDateTimeWidget(selectedReport)
          fillInvalidStartDateWidget()
          fillStartDateWidget()

          // timefine, single type
          checkSingleSelectWidget({
            report: selectedReport,
            identifier: 'duration',
          })
          cy.findByTestId('ReportForm-SingleSelect-duration-Selection').click()
          cy.findByTestId(`ReportForm-SingleSelect-duration-${defaultDuration}`).click()

          // start time
          fillTimeWidgetByClick({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.identifier ?? '',
            value: { hour: defaultStartTimeHour, minute: defaultStartTimeMinute },
          })

          turnOnOneTimeEmailSection()
          return submitReportAndClose()
        },
      },

      {
        name: 'When change the end date, send by date field will be updated to 1 day after the end date',
        fn: ({ setupIfFirstTest }) => {
          const today = DateTime.local()
          const endDate = today.plus({ days: 10 })
          const expectedSendDate = endDate.plus({ days: 1 })

          cy.intercept('POST', '/jsonrpc/public/index.php', handleApiCalls)

          setupIfFirstTest()

          // Report have date range and send by email
          testSelectReport(allReports[0])

          cy.findByTestId('ReportForm-SendEmailSwitch').find('input').check()

          cy.findByTestId('ReportForm-DateRange')
            .find('.MuiPickersSectionList-root')
            .first()
            .type(today.toFormat('yyyy/MM/dd'))
          cy.findByTestId('ReportForm-DateRange')
            .find('.MuiPickersSectionList-root')
            .last()
            .type(endDate.toFormat('yyyy/MM/dd'))

          cy.findByTestId('ReportForm-SendDate').should(
            'include.text',
            expectedSendDate
              .setLocale('en-ZA')
              .toLocaleString(DateTime.DATE_MED_WITH_WEEKDAY),
          )

          return testCloseExportDrawer()
        },
      },
      {
        name: 'Open form for Report 2, dateRange with DateTime with endDate being one day before send date, send date should be today',
        fn: ({ setupIfFirstTest }) => {
          const today = DateTime.local()
          const dateRange = [
            today.minus({ month: 1 }).set({ hour: 14, minute: 0, second: 0 }),
            today.minus({ day: 1 }).set({ hour: 23, minute: 55, second: 0 }),
          ] satisfies [DateTime, DateTime]
          const selectedReport = allReports[1]
          const submittedReport = {
            id: selectedReport.report_id,
            schedule: false,
            prompts: [
              {
                type: 'DATETIME',
                identifier: 'start_date',
                value: `${dateRange[0].toFormat('yyyy-LL-dd HH:mm:00ZZZ')}`,
              },
              {
                type: 'DATETIME',
                identifier: 'end_date',
                value: `${dateRange[1].toFormat('yyyy-LL-dd HH:mm:00ZZZ')}`,
              },
              {
                type: 'GEOFENCEGROUP',
                identifier: 'geofence_group',
                value: geofenceGroupsMock['323'].group_geofence_id,
              },
              {
                type: 'USER_NAME',
                identifier: 'username',
                value: dummyText,
              },
              {
                type: 'VEHLIST_NOALL',
                identifier: 'Vehicle Name',
                value: `g-${vehicleGroupsMock[0].id}`,
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: duxsMocks.user({}).mockState.settings.primaryEmail,
          }

          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  if (params.send_date) {
                    const sendDate = DateTime.fromFormat(
                      params.send_date,
                      'yyyy-LL-dd HH:mm:ssZZZ',
                    )
                    const currentTime = DateTime.local()

                    cyExpect(sendDate.year).toEqual(currentTime.year)
                    cyExpect(sendDate.month).toEqual(currentTime.month)
                    cyExpect(sendDate.day).toEqual(currentTime.day)
                    cyExpect(sendDate.hour).toEqual(currentTime.hour)
                  }
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.password).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.zipped).toEqual(undefined)
                  // eslint-disable-next-line
                  // eslint-disable-next-line unicorn/no-array-for-each
                  params.prompts.forEach((prompt, i) => {
                    cyExpect(prompt, `prompt: ${prompt.identifier}`).toDeepEqual(
                      submittedReport.prompts[i],
                    )
                  })

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // no all single, with group value
          fillRegistrationPromptSingleValue({
            value: vehicleGroupsMock[0].name,
            includeAll: false,
          })

          // date time range
          markDateTimeRangePrompt()
          checkDateTimeRangePrompt()
          fillValidDateTimeRangePrompt(dateRange)

          // geofence group
          checkGeofenceGroupPrompt(selectedReport)
          fillGeofenceGroupWidget()

          // have tested, fill only here
          fillTextInputPrompt({ identifier: 'username', type: 'one-time' })

          // only one file format
          cy.findByTestId('ReportForm-FileFormat')
            .children()
            .last()
            .should('have.text', selectedReport.file_formats[0].description)

          // no radio input
          cy.findByTestId('ReportForm-FileFormat').find('input').should('not.exist')
          return submitReportAndClose()
        },
      },
      {
        name: 'Open drawer for report including dateRange with Date which includes today, the send date should be 1 day later of end date',
        fn: ({ setupIfFirstTest }) => {
          const today = DateTime.local()
          const dateRange = [
            today.minus({ day: 5 }).set({ hour: 14, minute: 0, second: 0 }),
            today.plus({ day: 5 }).set({ hour: 23, minute: 0, second: 0 }),
          ] satisfies [DateTime, DateTime]
          const selectedReport = allReports[0]
          const submittedReport = {
            id: selectedReport.report_id,
            prompts: [
              {
                type: 'DATE',
                identifier: 'start_date',
                value: `${dateRange[0].toFormat('yyyy-LL-dd')} 00:00:00`,
              },
              {
                type: 'DATE',
                identifier: 'end_date',
                value: `${dateRange[1].toFormat('yyyy-LL-dd')} 23:59:59`,
              },
              {
                type: 'GEOFENCE',
                identifier: 'geofence_id',
                value: geofencesMock['2b'].geofence_id,
              },
              {
                type: 'DESC',
                identifier: 'km_cost',
                value: dummyText,
              },
              {
                type: 'FREE_TEXT',
                identifier: 'rest_days',
                value: '12,23',
              },
              {
                type: 'VEHLIST',
                identifier: 'Vehicle Name',
                value: 'all',
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: `${
              duxsMocks.user({}).mockState.settings.primaryEmail
            };${newValidEmail}`,
          }

          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.send_date).toEqual(
                    dateRange[1]
                      .plus({ day: 1 })
                      .startOf('day')
                      .toFormat('yyyy-LL-dd HH:mm:ssZZZ'),
                  )

                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.password).toEqual('passwor1')
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(params.zipped).toEqual(true)
                  // eslint-disable-next-line
                  // eslint-disable-next-line unicorn/no-array-for-each
                  params.prompts.forEach((prompt, i) => {
                    cyExpect(prompt, `prompt: ${prompt.identifier}`).toDeepEqual(
                      submittedReport.prompts[i],
                    )
                  })

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)

          // check default vehicle is all
          checkRegistrationPromptValue('All Vehicles')

          // date range
          markDateRangeStartEndComponent()
          fillValidDateRange(dateRange)

          // geofence
          fillGeofenceWidget(geofencesMock['2b'].geofence_name)

          // string type
          checkTextInputPrompt({
            report: selectedReport,
            identifier: 'km_cost',
            type: 'one-time',
          })
          fillTextInputPrompt({
            identifier: 'km_cost',
            type: 'one-time',
          })
          fillTextInputPrompt({
            identifier: 'rest_days',
            type: 'one-time',
            typeText: '12,23',
          })

          testEmailSection({
            report: selectedReport,
            type: 'one-time',
          })

          return submitReportAndClose()
        },
      },
    ],
  })

  // FIXME: this test causes pipeline ci failure, maybe because of the mifleet, separate it to make the test pass
  it('Open form for Report 5, mifleet, startDateTime, branch, list(updown)', () => {
    const selectedReport = allReports[4]
    const submittedReport = {
      id: selectedReport.report_id,
      schedule: false,
      prompts: [
        {
          identifier: 'start_date',
          type: 'DATETIME',
          value: `${validDateTimeRangeInput.start.toFormat('yyyy-LL-dd HH:mm:00ZZZ')}`,
        },
        {
          type: 'BRANCH',
          identifier: 'branch_id',
          value: dummyText,
        },
        {
          type: 'LIST(UP/DOWN/NONE)',
          identifier: 'updown',
          value: defaultUpDown,
        },
        {
          type: 'VEHLIST',
          identifier: 'Vehicle Name',
          value: `g-${vehicleGroupsMock[2].id}`,
        },
      ] satisfies Array<ReportPromptValueTypeApiInput>,
      delivery_type: 'Email',
      file_format: defaultFileFormat,
      emails: duxsMocks.user({}).mockState.settings.primaryEmail,
    }
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with(
          matchWithMethod<ExportReportNewApi.ApiInput>(
            req,
            'ct_fleet_submit_client_report_v3',
          ),
          ({ params }) => {
            cyExpect(params.file_format).toEqual(submittedReport.file_format)
            cyExpect(params.zipped).toEqual(undefined)
            cyExpect(params.password).toEqual(undefined)
            cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
            cyExpect(params.email_address).toEqual(submittedReport.emails)
            cyExpect(params.prompts).toDeepEqual(submittedReport.prompts)

            req.reply({ delay: 50, body: { id: 10, result: {} } })
          },
        )
        .otherwise(() => handleApiCalls(req))
    })

    setup()

    testSelectReport(selectedReport)

    // vehicle group
    fillRegistrationPromptSingleValue({
      value: vehicleGroupsMock[2].name,
      includeAll: true,
    })

    // start datetime
    checkStartDateOrStartDateTimeWidget(selectedReport)
    fillInvalidStartDateTimeWidget()
    fillStartDateTimeWidget()

    // List updown, single type
    cy.findByTestId('ReportForm-SingleSelect-updown-Selection').click()
    cy.findByTestId(`ReportForm-SingleSelect-updown-${defaultUpDown}`).click()

    // string type
    fillTextInputPrompt({ identifier: 'branch_id', type: 'one-time' })

    return submitReportAndClose()
  })
})

describe('Export Drawer for Recurring form', () => {
  const setup = () => {
    // NOTE: for the first case, wait to load the multiple requests
    mountAllReportsMain()
    cy.wait('@getFavouriteReports', { requestTimeout: 10000 })
  }

  runTestsInOrderWithLoggingAndSetup('passes several types of report forms', {
    setupIfFirstTest: setup,
    tests: [
      {
        name: 'Open drawer for report with geofence, desc, interval daily, data duration 1 week',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[0]
          const submittedReport = {
            id: selectedReport.report_id,
            prompts: [
              {
                type: 'GEOFENCE',
                identifier: 'geofence_id',
                value: geofencesMock['2b'].geofence_id,
              },
              {
                type: 'DESC',
                identifier: 'km_cost',
                value: dummyText,
              },
              {
                type: 'FREE_TEXT',
                identifier: 'rest_days',
                value: '12,23',
              },
              {
                type: 'VEHLIST',
                identifier: 'Vehicle Name',
                value: 'all',
              },
            ] satisfies Array<ReportPromptValueTypeApiInput>,
            delivery_type: 'Email',
            file_format: defaultFileFormat,
            emails: duxsMocks.user({}).mockState.settings.primaryEmail,
          }
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.id).toEqual(submittedReport.id)
                  cyExpect(params.file_format).toEqual(submittedReport.file_format)
                  cyExpect(params.schedule).toEqual(true)
                  cyExpect(params.repeat_interval).toEqual('1 day')
                  // cyExpect(params.schedule_end).toEqual('custom:1 weeks')
                  cyExpect(params.schedule_end).toEqual('1 weeks')
                  cyExpect(params.password).toEqual(undefined)
                  cyExpect(params.zipped).toEqual(undefined)
                  cyExpect(params.delivery_type).toEqual(submittedReport.delivery_type)
                  cyExpect(params.email_address).toEqual(submittedReport.emails)
                  cyExpect(
                    params.prompts.filter((prompt) => prompt.type !== 'DATE'),
                  ).toDeepEqual(submittedReport.prompts)

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)
          checkSelectRecurringForm()
          checkRepeatIntervalAndSendDateAndSummary()
          checkDataDuration()
          fillRegistrationPromptSingleValue({ value: 'All Vehicles', includeAll: true })
          fillGeofenceWidget(geofencesMock['2b'].geofence_name)
          fillTextInputPrompt({
            identifier: 'km_cost',
            type: 'recurring',
          })
          fillTextInputPrompt({
            identifier: 'rest_days',
            type: 'recurring',
            typeText: '12,23',
          })

          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 2 vehicle no all, geofence group, username, interval 1st month, data duration 1 week',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[1]
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.schedule).toEqual(true)
                  cyExpect(params.repeat_interval).toEqual('monthly:1')
                  cyExpect(params.schedule_end).toEqual('7 days')

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)
          checkSelectRecurringForm()
          // set interval as monthly
          fillIntervalSelection('Monthly')
          // data duration value would be effect by interval selection
          cy.findByTestId('ReportForm-Recurring-DataDuration')
            .find('input')
            .should('have.value', DataToReceiveDurationOptions.PREVIOUS_30_DAYS)
          // set data duration as previous 7 days
          fillDataDurationValue('Previous 7 days')
          fillRegistrationPromptSingleValue({
            value: vehiclesMock[0].name,
            includeAll: false,
          })
          fillGeofenceGroupWidget()
          fillTextInputPrompt({
            identifier: 'username',
            type: 'recurring',
          })
          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 3, with Driver, start/end time, vehicle no all no group, number, interval weekly 3,6, data duration last month',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[2]
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.schedule).toEqual(true)
                  cyExpect(params.repeat_interval).toEqual('weekly:3,6')
                  cyExpect(params.schedule_end).toEqual('previous month')

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)
          checkSelectRecurringForm()
          // set interval as weekly
          fillIntervalSelection('Weekly')
          // data duration value would be effect by interval selection
          cy.findByTestId('ReportForm-Recurring-DataDuration')
            .find('input')
            .should('have.value', DataToReceiveDurationOptions.PREVIOUS_7_DAYS)
          cy.findByTestId('ReportForm-Recurring-DaysOfWeek-3').find('input').check()
          cy.findByTestId('ReportForm-Recurring-DaysOfWeek-6').find('input').check()

          fillDataDurationValue('Previous month')
          fillRegistrationPromptSingleValue({
            value: vehiclesMock[0].name,
            includeAll: false,
          })
          fillDriverAndGroupWidget(driversMock[0].name)
          fillTimeWidgetByType({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.identifier ?? '',
            value: { time: `${defaultStartTimeHour}:${defaultStartTimeMinute}` },
          })
          fillTimeWidgetByClick({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('end'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('end'),
              )?.identifier ?? '',
            value: {
              hour: defaultEndTime.split(':')[0],
              minute: defaultEndTime.split(':')[1],
            },
          })
          fillNumberInputWidget({
            identifier: 'in_interval',
            type: 'recurring',
          })
          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 4, vehicle multiple, driver no all, timefine, interval 3 week, data duration 2 weeks',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[3]
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.schedule).toEqual(true)
                  cyExpect(params.repeat_interval).toEqual('3 week')
                  // cyExpect(params.schedule_end).toEqual('custom:2 weeks')
                  cyExpect(params.schedule_end).toEqual('2 weeks')

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)
          checkSelectRecurringForm()
          // set interval as custom
          fillIntervalSelection('Custom interval')
          cy.findByTestId('ReportForm-Recurring-Custom-Interval').click()
          cy.findByTestId('ReportForm-Recurring-Custom-Interval-Weeks').click()
          cy.findByTestId('ReportForm-Recurring-Custom-Frequency').type(
            '{selectAll}{del}3',
          )
          // data duration value would be effect by interval selection
          cy.findByTestId('ReportForm-Recurring-DataDuration')
            .find('input')
            .should('have.value', DataToReceiveDurationOptions.CUSTOM_DURATION)
          // set data duration as custom
          fillDataDurationValue('Custom duration')
          cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit').click()
          cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Unit-Weeks').click()
          cy.findByTestId('ReportForm-Recurring-DataDuration-Custom-Amount').type(
            '{selectAll}{del}2',
          )
          fillRegistrationMultipleWidget({
            vehicle: vehiclesMock[0].name,
            group: vehicleGroupsMock[0].name,
          })
          // fill driver
          fillDriverAndGroupWidget(driversMock[0].name)
          // timefine, single type
          cy.findByTestId('ReportForm-SingleSelect-duration-Selection').click()
          cy.findByTestId(`ReportForm-SingleSelect-duration-${defaultDuration}`).click()
          // start time
          fillTimeWidgetByClick({
            name:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.name ?? '',
            identifier:
              selectedReport.prompts.find(
                (p) => p.type.startsWith('TIME') && p.identifier.includes('start'),
              )?.identifier ?? '',
            value: { hour: defaultStartTimeHour, minute: defaultStartTimeMinute },
          })
          return submitReportAndClose()
        },
      },
      {
        name: 'Open form for Report 5, mifleet, branch, list(updown), interval 25th monthly, data duration todate',
        fn: ({ setupIfFirstTest }) => {
          const selectedReport = allReports[4]
          cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
            match(req.body)
              .with(
                matchWithMethod<ExportReportNewApi.ApiInput>(
                  req,
                  'ct_fleet_submit_client_report_v3',
                ),
                ({ params }) => {
                  cyExpect(params.schedule).toEqual(true)
                  cyExpect(params.repeat_interval).toEqual('monthly:25')
                  cyExpect(params.schedule_end).toEqual('todate')

                  req.reply({ delay: 50, body: { id: 10, result: {} } })
                },
              )
              .otherwise(() => handleApiCalls(req))
          })

          setupIfFirstTest()

          testSelectReport(selectedReport)
          checkSelectRecurringForm()
          fillRegistrationPromptSingleValue({
            value: vehicleGroupsMock[0].name,
            includeAll: true,
          })
          // List updown, single type
          cy.findByTestId('ReportForm-SingleSelect-updown-Selection').click()
          cy.findByTestId(`ReportForm-SingleSelect-updown-${defaultUpDown}`).click()
          // string type
          fillTextInputPrompt({
            identifier: 'branch_id',
            type: 'recurring',
          })
          // set interval as monthly
          fillIntervalSelection('Monthly')
          cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom')
            .find('input')
            .check()
          cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-Select').click()
          cy.findByTestId('ReportForm-Recurring-DayOfMonth-Custom-25').click()
          // data duration value would be effect by interval selection
          cy.findByTestId('ReportForm-Recurring-DataDuration')
            .find('input')
            .should('have.value', DataToReceiveDurationOptions.PREVIOUS_30_DAYS)
          // set data duration as custom
          fillDataDurationValue('Beginning of the month till date')
          return submitReportAndClose()
        },
      },
    ],
  })
})
