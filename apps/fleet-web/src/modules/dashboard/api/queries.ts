import { isEmpty } from 'lodash'
import * as Sentry from '@sentry/browser'
import {
  useMutation,
  useQuery,
  useQueryClient,
  type QueryObserverOptions,
} from '@tanstack/react-query'
import { useDispatch } from 'react-redux'

import {
  makeMutationErrorHandlerWithToast,
  makeQueryErrorHandlerWithToast,
} from 'api/helpers'
import sisenseAPI from 'api/sisense'
import { getLocale, sisenseLoginSuccess } from 'duxs/user'
import dashboardApi from 'src/modules/dashboard/api'
import { invalidateQueriesOnEntitiesMutation } from 'src/modules/shared/utils'
import { useTypedSelector } from 'src/redux-hooks'
import { ctToast } from 'src/util-components/ctToast'
import { minutesToMs } from 'src/util-functions/functional-utils'
import { createQuery } from 'src/util-functions/react-query-utils'

import type { DashboardType, FetchDashboardTopicQuery } from './types'

const API_RETRY_TIMES = 2

async function retryApiCall<T>(
  apiFunction: () => Promise<T>,
  maxRetries: number,
): Promise<T> {
  let retries = 0
  while (retries < maxRetries) {
    try {
      const result = await apiFunction()
      return result // Success, return the result
    } catch (error) {
      console.error(`API call failed (retry ${retries + 1}):`, error)
      retries++
    }
  }
  throw new Error(`API call failed after ${maxRetries} retries`)
}

/**************************************** Widgets mutation ******************************************/

export const useSaveDashboardMaintenancePromptMutation = ({
  dashboardType,
}: {
  dashboardType: DashboardType
}) => {
  const queryClient = useQueryClient()
  const locale = useTypedSelector(getLocale)?.split('-')[0] ?? ''
  return useMutation({
    mutationFn: () => dashboardApi.saveDashboardMaintenancePrompt(),
    onSettled: () =>
      queryClient.invalidateQueries(
        sisenseConfigurationQuery({ dashboardType, locale }),
      ),
    ...makeMutationErrorHandlerWithToast(),
  })
}

export function useAddWidgetMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({
      widgetsId,
      dashboardType,
    }: {
      widgetsId: Array<string>
      dashboardType: DashboardType
    }) => dashboardApi.addWidgets(`{${widgetsId.join(',')}}`, dashboardType),
    onSuccess: (_, { widgetsId }) => {
      invalidateQueriesOnEntitiesMutation(queryClient, {
        entities: ['dashboard_widget'],
      })

      const isPlural = widgetsId.length > 1
      ctToast.fire(
        'success',
        (isPlural
          ? 'New widgets added successfully'
          : 'New widget added successfully') + '.',
      )
    },
    ...makeMutationErrorHandlerWithToast(),
  })
}

/**************************************** Fetch dashboard topics and status ******************************************/

const dashboardTopicQuery = ({
  dashboardIndustryID,
  dashboardType,
}: FetchDashboardTopicQuery.ApiInput) =>
  ({
    queryKey: [
      'dashboard/dashboardTopicQuery',
      { dashboardIndustryID, dashboardType },
    ] as const,
    queryFn: () => dashboardApi.getDashboardTopics(dashboardIndustryID, dashboardType),
    ...makeQueryErrorHandlerWithToast(),
  }) satisfies QueryObserverOptions

export function useDashboardTopicQuery(props: FetchDashboardTopicQuery.ApiInput) {
  return useQuery(dashboardTopicQuery(props))
}

export const fetchLiveDashboardStatusQuery = () =>
  createQuery({
    queryKey: ['dashboard/fetchLiveDashboardStatus'] as const,
    queryFn: async () => dashboardApi.getLiveDashboardStatus(),
    staleTime: minutesToMs(0.5),
    ...makeQueryErrorHandlerWithToast(),
  })

export const useFetchLiveDashboardStatusQuery = () =>
  useQuery(fetchLiveDashboardStatusQuery())

/**************************************** Fetch widgets with/without topic id ******************************************/

export const fetchAllUnselectedWidgetsQuery = ({
  industry,
  ...rest
}: {
  locale: string
  industry: string
  dashboardType: DashboardType
}) =>
  createQuery({
    queryKey: ['dashboard/fetchAllUnselectedWidgets', { ...rest, industry }] as const,
    queryFn: () =>
      dashboardApi.getUnselectedWidgets({ ...rest, dashboardIndustryID: industry }),
    staleTime: minutesToMs(0.5),
    ...makeQueryErrorHandlerWithToast(),
  })

export const useFetchAllUnselectedWidgetsQuery = (props: {
  locale: string
  industry: string
  dashboardType: DashboardType
}) => useQuery(fetchAllUnselectedWidgetsQuery(props))

const fetchDashboardWidgetsQueryCreateKey = (props: {
  locale: string
  industry: string
  dashboardType: DashboardType
}) => ['dashboard/fetchDashboardWidgets', props] as const

export const fetchDashboardWidgetsQuery = ({
  locale,
  industry,
  dashboardType,
}: {
  locale: string
  industry: string
  dashboardType: DashboardType
}) =>
  createQuery({
    queryKey: fetchDashboardWidgetsQueryCreateKey({
      locale,
      industry,
      dashboardType,
    }),
    queryFn: async () =>
      dashboardApi.getDashboard(locale, industry, null, dashboardType),
    staleTime: minutesToMs(0.5),
    ...makeQueryErrorHandlerWithToast(),
  })

export const useFetchDashboardWidgetsQuery = (props: {
  locale: string
  industry: string
  dashboardType: DashboardType
}) => useQuery(fetchDashboardWidgetsQuery(props))

export const fetchWidgetsWithTopicQuery = ({
  locale,
  industry,
  dashboardType,
  topic,
}: {
  locale: string
  industry: string
  dashboardType: DashboardType
  topic: string | null
}) =>
  createQuery({
    queryKey: [
      'dashboard/fetchDashboardWidgets',
      { locale, industry, dashboardType, topic },
    ] as const,
    queryFn: () => dashboardApi.getDashboard(locale, industry, topic, dashboardType),
    staleTime: minutesToMs(0.5),
    enabled: !!topic,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useFetchWidgetsWithTopicQuery = (props: {
  locale: string
  industry: string
  dashboardType: DashboardType
  topic: string | null
}) => useQuery(fetchWidgetsWithTopicQuery(props))

/**************************************** Sisense login ******************************************/

export const useSisenseLoginMutation = () => {
  const dispatch = useDispatch()

  return useMutation({
    mutationFn: () => retryApiCall(sisenseAPI.sisenseLogin, API_RETRY_TIMES),
    onSuccess(data) {
      dispatch(
        sisenseLoginSuccess({
          sisenseApi: data.sisenseApi,
          sisenseJsUrl: data.sisenseJsUrl,
        }),
      )
    },
    gcTime: Infinity, // get the sisense jwt token
    onError: (error) => {
      reportDashboardErrorToSentry({
        error,
        message: 'Failed to sisense login!',
      })
    },
  })
}

/**************************************** Sisense authentication ******************************************/

export const useSisenseAuthenticationMutation = ({
  sisenseURL,
}: {
  sisenseURL: string
}) => {
  const sisenseAuthUrl = `${sisenseURL}/api/auth/isauth`

  return useMutation({
    mutationFn: () =>
      retryApiCall(async (): Promise<{ isAuthenticated: boolean }> => {
        const result = await fetch(sisenseAuthUrl, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        return result.json()
      }, API_RETRY_TIMES),
    onError: (error) => {
      reportDashboardErrorToSentry({
        error,
        message: `Failed to auth with sisense with url: ${sisenseAuthUrl}`,
      })
    },
  })
}

/**************************************** Sisense configuration ******************************************/

export const sisenseConfigurationQuery = ({
  locale,
  dashboardType,
}: {
  locale: string
  dashboardType: DashboardType
}) =>
  createQuery({
    queryKey: [
      'dashboard/sisenseConfigurationQuery',
      { locale, dashboardType },
    ] as const,
    queryFn: () => dashboardApi.getSisenseConfiguration(locale, dashboardType),
    staleTime: 2000,
    ...makeQueryErrorHandlerWithToast(),
  })

export function useSisenseConfigurationQuery(dashboardType: DashboardType) {
  const locale = useTypedSelector(getLocale)?.split('-')[0] ?? ''

  return useQuery(sisenseConfigurationQuery({ locale, dashboardType }))
}

export const useSisenseConfigurationIfNeededQuery = (dashboardType: DashboardType) => {
  const locale = useTypedSelector(getLocale)?.split('-')[0] ?? ''
  return useQuery({
    ...sisenseConfigurationQuery({ locale, dashboardType }),
    refetchOnMount: (query) =>
      query.state.data === undefined || isEmpty(query.state.data?.mainDashboardId),
  })
}

export const useSaveSisenseConfigurationMutation = () => {
  const queryClient = useQueryClient()
  const locale = useTypedSelector(getLocale)?.split('-')[0] ?? ''

  return useMutation({
    mutationFn: ({ dashboardType }: { dashboardType: DashboardType }) =>
      // NOTE: currently it's only used for live dashboard, update it when needed
      dashboardApi.saveSisenseConfiguration({
        locale,
        dashboardType,
        topics: '',
        industry: '',
      }),
    onSettled: (_data, _error, variables) =>
      queryClient.invalidateQueries(
        sisenseConfigurationQuery({ ...variables, locale }),
      ),
    ...makeMutationErrorHandlerWithToast(),
  })
}

export const fetchLoadJwtAuthQuery = ({
  url,
  enabled,
}: {
  url: string
  enabled: boolean
}) =>
  createQuery({
    queryKey: ['dashboard/script_query', 'sisense/jwt/auth'] as const,
    queryFn: () =>
      // stop the redirect from the response
      ENV.CYPRESS_CT_ENV
        ? Promise.resolve(new Response())
        : fetch(url, { credentials: 'include', redirect: 'manual' }).then(
            (response) => {
              console.log(`Fetching JWT auth from ${url}`, response)
              // Check if it's a redirect response
              if (response.type === 'opaqueredirect' || response.status === 302) {
                throw new Error(
                  'JWT authentication failed - received redirect response',
                )
              }

              // Check for other non-success status codes
              if (!response.ok) {
                throw new Error(
                  `JWT authentication failed - status: ${response.status}`,
                )
              }

              return response
            },
          ),
    enabled,
  })

export const useLoadJwtAuthQuery = (props: { url: string; enabled: boolean }) =>
  useQuery(fetchLoadJwtAuthQuery(props))

export const reportDashboardErrorToSentry = ({
  error,
  message,
}: {
  error?: Error
  message: string
}) => {
  Sentry.withScope((scope) => {
    scope.setTag('module', 'dashboard-sisense')

    // Group error by error name
    Sentry.captureException(error ?? new Error(message))
  })
}
